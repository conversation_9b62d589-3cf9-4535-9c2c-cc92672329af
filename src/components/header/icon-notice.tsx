import { useState, useRef, useEffect, FC } from 'react'
import IconButton from './icon-button'
import noticeApi from '@/services/api/notice'
import { useRouter } from 'next/router'

interface NoticeType {
  content: string
  id: number | string
  read: number //0 未读  1 已读
  sendTime: string
  title: string
  type: number
}

interface NotificationsProps {
  hasLogin: boolean
  notices: NoticeType[]
  handleNoticesClick?: (val: NoticeType) => void
}

enum tagMap {
  'ChangLog' = 0,
  'Message' = 1,
}

export function Notifications({
  notices,
  hasLogin,
  handleNoticesClick,
}: NotificationsProps) {
  const router = useRouter()

  // 查看消息列表
  const handleNoticeAll = () => {
    const { pathname } = router
    if (pathname === '/workbench/notifications') return
    if (hasLogin) {
      router.push('/workbench/notifications')
    } else {
      window.location.href = `${process.env.NEXT_PUBLIC_LOGIN_URL}/login.html?redirect=${window.location.href}&source=api`
    }
  }
  //处理消息点击事件
  const handleNoticeClick = async (item: NoticeType) => {
    if (item.id) {
      if (!item.type) {
        // 发送消息已读状态更新
        await noticeApi.updateNoticeStatus(Number(item.id))
        // 抛出点击事件
        if (handleNoticesClick) {
          handleNoticesClick(item)
        }
      }
      router.push(`/workbench/notifications?id=${item.id}`)
    }
  }
  if (!notices.length) {
    return (
      <div className="rounded-lg bg-white p-4 text-[#19191a]">
        <p>{`You don't have any notifications right now.`}</p>
      </div>
    )
  }

  return (
    <div className="rounded-lg bg-white">
      <div className="box-content flex h-5 items-center justify-between p-[16px]">
        <span className="text-base font-semibold">Notifications</span>
        <span
          onClick={handleNoticeAll}
          className="cursor-pointer text-[13px] text-xs text-textColorLink"
        >
          View all
        </span>
      </div>
      {notices.map((data, index) => {
        return (
          <div
            className="flex cursor-pointer flex-col px-[16px] py-[8px] text-xs transition-all hover:bg-[#F2F2F2]"
            key={index}
            onClick={() => handleNoticeClick(data)}
          >
            <div className="flex items-center text-textColorBlack">
              {!data.read && (
                <span className="mr-[6px] size-2 rounded-full bg-[#C00000]"></span>
              )}
              <span
                className={`${!data.read && 'text-[12px]/[20px] font-semibold'}`}
              >
                {data.title}
              </span>
              {data.type && (
                <span
                  className={`ml-auto flex h-5 items-center rounded px-1 font-semibold ${data.type === 1 ? 'bg-[#D6F5DF] text-[#09832A]' : 'bg-[#ECDCF9] text-[#512971]'}`}
                >
                  {tagMap[data.type]}
                </span>
              )}
            </div>
            <div className="text-textColorGray">{data.sendTime}</div>
          </div>
        )
      })}
    </div>
  )
}

const Icon: FC<{ hasUnRead: boolean }> = (props) => {
  const { hasUnRead, ...restProps } = props
  if (!hasUnRead) {
    return (
      <svg
        xmlns="http://www.w3.org/2000/svg"
        fill="none"
        version="1.1"
        {...restProps}
        width="18.7332763671875"
        height="20.3193359375"
        viewBox="0 0 18.7332763671875 20.3193359375"
      >
        <g>
          <g>
            <path
              d="M15.664151171875,16.7001953125C15.683651171875,16.7019953125,15.703351171875,16.7028953125,15.723251171875,16.7028953125C15.743151171875,16.7028953125,15.762951171875,16.7019953125,15.782451171875,16.7001953125L18.533251171875,16.7001953125L18.533251171875,15.2001953125L16.410751171875,15.2001953125L16.410751171875,7.9501953125Q16.410751171875,4.7400353125,14.330051171875,2.4701153125Q12.249251171875,0.2001953125,9.306611171875,0.2001953125Q6.363971171875,0.2001953125,4.283211171875,2.4701153125Q2.202451171875,4.7400353125,2.202451171875,7.9501953125L2.202451171875,15.2001953125L0.199951171875,15.2001953125L0.199951171875,16.7001953125L2.830821171875,16.7001953125C2.850311171875,16.7019953125,2.870031171875,16.7028953125,2.889951171875,16.7028953125C2.909871171875,16.7028953125,2.929591171875,16.7019953125,2.949081171875,16.7001953125L6.209281171875,16.7001953125Q6.285641171875,18.0660953125,7.216171171875,19.0530953125Q8.221761171875,20.1195953125,9.639951171875,20.1195953125Q11.058151171875,20.1195953125,12.063751171875,19.0530953125Q12.994251171875,18.0660953125,13.070651171875,16.7001953125L15.664151171875,16.7001953125ZM3.577451171875,15.2001953125L15.035751171875,15.2001953125L15.035751171875,7.9501953125Q15.035751171875,5.3613553125,13.357751171875,3.5307753125Q11.679751171875,1.7001953125,9.306611171875,1.7001953125Q6.933511171875,1.7001953125,5.255481171875,3.5307753125Q3.577451171875,5.3613553125,3.577451171875,7.9501953125L3.577451171875,15.2001953125ZM7.589431171875,16.7001953125L11.690451171875,16.7001953125Q11.617851171875,17.4338953125,11.105251171875,17.9775953125Q10.499951171875,18.6195953125,9.639951171875,18.6195953125Q8.779991171875,18.6195953125,8.174651171875,17.9775953125Q7.662031171875,17.4338953125,7.589431171875,16.7001953125Z"
              fillRule="evenodd"
              fill="#19191A"
              fillOpacity="1"
            />
            <path
              d="M15.682551171875,16.5010953125Q15.702851171875,16.5028953125,15.723251171875,16.5028953125Q15.743751171875,16.5028953125,15.763951171875,16.5010953125L15.773151171875,16.5001953125L18.533251171875,16.5001953125L18.533251171875,16.7001953125L18.333251171875,16.7001953125L18.333251171875,15.2001953125L18.533251171875,15.2001953125L18.533251171875,15.4001953125L16.210751171875,15.4001953125L16.210751171875,7.9501953125Q16.210751171875,4.8178253125,14.182551171875,2.6052553125Q12.161251171875,0.4001923125,9.306611171875,0.4001953125Q6.451961171875,0.4001953125,4.430641171875,2.6052553125Q2.402451171875,4.8178353125,2.402451171875,7.9501953125L2.402451171875,15.4001953125L0.199951171875,15.4001953125L0.199951171875,15.2001953125L0.399951171875,15.2001953125L0.399951171875,16.7001953125L0.199951171875,16.7001953125L0.199951171875,16.5001953125L2.840051171875,16.5001953125L2.849251171875,16.5010953125Q2.869491171875,16.5028953125,2.889951171875,16.5028953125Q2.910411171875,16.5028953125,2.930651171875,16.5010953125L2.939851171875,16.5001953125L6.398411171875,16.5001953125L6.408971171875,16.6889953125Q6.481261171875,17.9820953125,7.361691171875,18.9158953125Q8.308071171875,19.9195953125,9.639951171875,19.9195953125Q10.971851171875,19.9195953125,11.918251171875,18.9158953125Q12.798651171875,17.9820953125,12.870951171875,16.6889953125L12.881451171875,16.5001953125L15.673351171875,16.5001953125L15.682551171875,16.5010953125ZM15.645751171875,16.8992953125L15.664151171875,16.7001953125L15.664151171875,16.9001953125L13.070651171875,16.9001953125L13.070651171875,16.7001953125L13.270351171875,16.7113953125Q13.189851171875,18.1501953125,12.209251171875,19.1902953125Q11.144451171875,20.3195953125,9.639951171875,20.3195953125Q8.135451171875001,20.3195953125,7.070651171875,19.1902953125Q6.090031171875,18.1501953125,6.009591171875,16.7113953125L6.209281171875,16.7001953125L6.209281171875,16.9001953125L2.949081171875,16.9001953125L2.949081171875,16.7001953125L2.967521171875,16.8992953125Q2.928861171875,16.9028953125,2.889951171875,16.9028953125Q2.851041171875,16.9028953125,2.812381171875,16.8992953125L2.830821171875,16.7001953125L2.830821171875,16.9001953125L-0.0000488281250000111,16.9001953125L-0.0000488281250000111,15.0001953125L2.202451171875,15.0001953125L2.202451171875,15.2001953125L2.002451171875,15.2001953125L2.002451171875,7.9501953125Q2.002451171875,4.6622353125,4.135781171875,2.3349653125Q6.276001171875,0.0001953124999999889,9.306611171875,0.0001953124999999889Q12.337251171875,0.0001963124999999899,14.477451171875,2.3349653125Q16.610751171875,4.6622353125,16.610751171875,7.9501953125L16.610751171875,15.2001953125L16.410751171875,15.2001953125L16.410751171875,15.0001953125L18.733251171875,15.0001953125L18.733251171875,16.9001953125L15.782451171875,16.9001953125L15.782451171875,16.7001953125L15.800851171875,16.8992953125Q15.762151171875,16.9028953125,15.723251171875,16.9028953125Q15.684351171875,16.9028953125,15.645751171875,16.8992953125ZM3.577451171875,15.0001953125L15.035751171875,15.0001953125L15.035751171875,15.2001953125L14.835751171875,15.2001953125L14.835751171875,7.9501953125Q14.835751171875,5.4391453125,13.210351171875,3.6659153125Q11.591751171875,1.9001953125,9.306611171875,1.9001953125Q7.021491171875,1.9001953125,5.402911171875,3.6659153125Q3.777451171875,5.4391553125,3.777451171875,7.9501953125L3.777451171875,15.2001953125L3.577451171875,15.2001953125L3.577451171875,15.0001953125ZM3.377451171875,15.4001953125L3.377451171875,7.9501953125Q3.377451171875,5.2835653125,5.108051171875,3.3956253125Q6.845531171875,1.5001953125,9.306611171875,1.5001953125Q11.767651171875,1.5001953125,13.505151171875,3.3956253125Q15.235751171875,5.2835553125,15.235751171875,7.9501953125L15.235751171875,15.4001953125L3.577451171875,15.4001953125L3.377451171875,15.4001953125ZM7.589431171875,16.5001953125L11.911251171875,16.5001953125L11.889451171875,16.7198953125Q11.810151171875,17.5214953125,11.250751171875,18.1147953125Q10.586251171875,18.8195953125,9.639951171875,18.8195953125Q8.693681171875,18.8195953125,8.029141171875,18.1147953125Q7.469721171875,17.5214953125,7.390401171875,16.7198953125L7.368661171875,16.5001953125L7.589431171875,16.5001953125ZM7.589431171875,16.9001953125L7.589431171875,16.7001953125L7.788461171875,16.6804953125Q7.854341171875,17.3462953125,8.320171171875,17.8403953125Q8.866301171875,18.4195953125,9.639951171875,18.4195953125Q10.413551171875,18.4195953125,10.959751171875,17.8403953125Q11.425551171875,17.3462953125,11.491451171875,16.6804953125L11.690451171875,16.7001953125L11.690451171875,16.9001953125L7.589431171875,16.9001953125Z"
              fill="#19191A"
              fillOpacity="1"
            />
          </g>
        </g>
      </svg>
    )
  } else {
    return (
      <svg
        xmlns="http://www.w3.org/2000/svg"
        fill="none"
        version="1.1"
        width="22"
        height="23.5"
        viewBox="0 0 22 23.5"
        {...restProps}
      >
        <g>
          <g>
            <g>
              <path
                d="M17.297696093749998,18.9000244140625C17.31719609375,18.9018244140625,17.33689609375,18.9027244140625,17.35679609375,18.9027244140625C17.37669609375,18.9027244140625,17.396496093750002,18.9018244140625,17.41599609375,18.9000244140625L20.16679609375,18.9000244140625L20.16679609375,17.4000244140625L18.04429609375,17.4000244140625L18.04429609375,10.1500244140625Q18.04429609375,6.9398644140625,15.96359609375,4.6699444140625Q13.88279609375,2.4000244140625,10.94015609375,2.4000244140625Q7.99751609375,2.4000244140625,5.91675609375,4.6699444140625Q3.83599609375,6.9398644140625,3.83599609375,10.1500244140625L3.83599609375,17.4000244140625L1.83349609375,17.4000244140625L1.83349609375,18.9000244140625L4.46436609375,18.9000244140625C4.48385609375,18.9018244140625,4.5035760937500005,18.9027244140625,4.5234960937499995,18.9027244140625C4.54341609375,18.9027244140625,4.56313609375,18.9018244140625,4.58262609375,18.9000244140625L7.84282609375,18.9000244140625Q7.91918609375,20.2659244140625,8.84971609375,21.2529244140625Q9.85530609375,22.3194244140625,11.27349609375,22.3194244140625Q12.69169609375,22.3194244140625,13.69729609375,21.2529244140625Q14.62779609375,20.2659244140625,14.70419609375,18.9000244140625L17.297696093749998,18.9000244140625ZM5.2109960937499995,17.4000244140625L16.66929609375,17.4000244140625L16.66929609375,10.1500244140625Q16.66929609375,7.5611844140625,14.99129609375,5.730604414062499Q13.31329609375,3.9000244140625,10.94015609375,3.9000244140625Q8.56705609375,3.9000244140625,6.88902609375,5.730604414062499Q5.2109960937499995,7.5611844140625,5.2109960937499995,10.1500244140625L5.2109960937499995,17.4000244140625ZM9.222976093749999,18.9000244140625L13.32399609375,18.9000244140625Q13.25139609375,19.6337244140625,12.73879609375,20.1774244140625Q12.13349609375,20.8194244140625,11.27349609375,20.8194244140625Q10.41353609375,20.8194244140625,9.80819609375,20.1774244140625Q9.29557609375,19.6337244140625,9.222976093749999,18.9000244140625Z"
                fillRule="evenodd"
                fill="#19191A"
                fillOpacity="1"
              />
              <path
                d="M17.316096093749998,18.7009244140625Q17.33639609375,18.7027244140625,17.35679609375,18.7027244140625Q17.377296093749997,18.7027244140625,17.39749609375,18.7009244140625L17.40669609375,18.7000244140625L20.16679609375,18.7000244140625L20.16679609375,18.9000244140625L19.96679609375,18.9000244140625L19.96679609375,17.4000244140625L20.16679609375,17.4000244140625L20.16679609375,17.6000244140625L17.84429609375,17.6000244140625L17.84429609375,10.1500244140625Q17.84429609375,7.0176544140625,15.81609609375,4.805084414062501Q13.79479609375,2.6000214140625,10.94015609375,2.6000244140625Q8.08550609375,2.6000244140625,6.06418609375,4.805084414062501Q4.035996093750001,7.0176644140625,4.035996093750001,10.1500244140625L4.035996093750001,17.6000244140625L1.83349609375,17.6000244140625L1.83349609375,17.4000244140625L2.03349609375,17.4000244140625L2.03349609375,18.9000244140625L1.83349609375,18.9000244140625L1.83349609375,18.7000244140625L4.47359609375,18.7000244140625L4.48279609375,18.7009244140625Q4.50303609375,18.7027244140625,4.5234960937499995,18.7027244140625Q4.543956093749999,18.7027244140625,4.564196093750001,18.7009244140625L4.5733960937500004,18.7000244140625L8.03195609375,18.7000244140625L8.042516093749999,18.8888244140625Q8.114806093750001,20.1819244140625,8.99523609375,21.1157244140625Q9.94161609375,22.1194244140625,11.27349609375,22.1194244140625Q12.60539609375,22.1194244140625,13.55179609375,21.1157244140625Q14.43219609375,20.1819244140625,14.50449609375,18.8888244140625L14.51499609375,18.7000244140625L17.306896093749998,18.7000244140625L17.316096093749998,18.7009244140625ZM17.27929609375,19.0991244140625L17.297696093749998,18.9000244140625L17.297696093749998,19.1000244140625L14.70419609375,19.1000244140625L14.70419609375,18.9000244140625L14.90389609375,18.9112244140625Q14.82339609375,20.3500244140625,13.84279609375,21.3901244140625Q12.77799609375,22.5194244140625,11.27349609375,22.5194244140625Q9.768996093750001,22.5194244140625,8.70419609375,21.3901244140625Q7.72357609375,20.3500244140625,7.64313609375,18.9112244140625L7.84282609375,18.9000244140625L7.84282609375,19.1000244140625L4.58262609375,19.1000244140625L4.58262609375,18.9000244140625L4.60106609375,19.0991244140625Q4.56240609375,19.1027244140625,4.5234960937499995,19.1027244140625Q4.48458609375,19.1027244140625,4.44592609375,19.0991244140625L4.46436609375,18.9000244140625L4.46436609375,19.1000244140625L1.63349609375,19.1000244140625L1.63349609375,17.2000244140625L3.83599609375,17.2000244140625L3.83599609375,17.4000244140625L3.63599609375,17.4000244140625L3.63599609375,10.1500244140625Q3.63599609375,6.8620644140625,5.76932609375,4.5347944140625Q7.90954609375,2.2000244140625,10.94015609375,2.2000244140625Q13.97079609375,2.2000254140625,16.11099609375,4.5347944140625Q18.24429609375,6.8620644140625,18.24429609375,10.1500244140625L18.24429609375,17.4000244140625L18.04429609375,17.4000244140625L18.04429609375,17.2000244140625L20.36679609375,17.2000244140625L20.36679609375,19.1000244140625L17.41599609375,19.1000244140625L17.41599609375,18.9000244140625L17.43439609375,19.0991244140625Q17.39569609375,19.1027244140625,17.35679609375,19.1027244140625Q17.31789609375,19.1027244140625,17.27929609375,19.0991244140625ZM5.2109960937499995,17.2000244140625L16.66929609375,17.2000244140625L16.66929609375,17.4000244140625L16.46929609375,17.4000244140625L16.46929609375,10.1500244140625Q16.46929609375,7.6389744140625,14.84389609375,5.8657444140625Q13.22529609375,4.1000244140625,10.94015609375,4.1000244140625Q8.655036093749999,4.1000244140625,7.03645609375,5.8657444140625Q5.410996093750001,7.6389844140625,5.410996093750001,10.1500244140625L5.410996093750001,17.4000244140625L5.2109960937499995,17.4000244140625L5.2109960937499995,17.2000244140625ZM5.01099609375,17.6000244140625L5.01099609375,10.1500244140625Q5.01099609375,7.4833944140625,6.74159609375,5.5954544140625Q8.479076093749999,3.7000244140625,10.94015609375,3.7000244140625Q13.40119609375,3.7000244140625,15.13869609375,5.5954544140625Q16.869296093750002,7.4833844140625,16.869296093750002,10.1500244140625L16.869296093750002,17.6000244140625L5.2109960937499995,17.6000244140625L5.01099609375,17.6000244140625ZM9.222976093749999,18.7000244140625L13.54479609375,18.7000244140625L13.52299609375,18.9197244140625Q13.44369609375,19.7213244140625,12.88429609375,20.3146244140625Q12.21979609375,21.0194244140625,11.27349609375,21.0194244140625Q10.32722609375,21.0194244140625,9.66268609375,20.3146244140625Q9.103266093750001,19.7213244140625,9.02394609375,18.9197244140625L9.00220609375,18.7000244140625L9.222976093749999,18.7000244140625ZM9.222976093749999,19.1000244140625L9.222976093749999,18.9000244140625L9.42200609375,18.8803244140625Q9.48788609375,19.5461244140625,9.95371609375,20.0402244140625Q10.49984609375,20.6194244140625,11.27349609375,20.6194244140625Q12.04709609375,20.6194244140625,12.59329609375,20.0402244140625Q13.05909609375,19.5461244140625,13.12499609375,18.8803244140625L13.32399609375,18.9000244140625L13.32399609375,19.1000244140625L9.222976093749999,19.1000244140625Z"
                fill="#19191A"
                fillOpacity="1"
              />
            </g>
          </g>
          <g>
            <path
              d="M19.889073437500002,4.740679296875Q19.889073437500002,4.944529296875,19.8689234375,5.147199296875Q19.8487734375,5.349859296875,19.8096634375,5.550159296875Q19.769363437499997,5.749269296875,19.710103437500003,5.944829296875Q19.6508434375,6.140379296875,19.5738134375,6.327639296875Q19.4955834375,6.517269296875,19.3995834375,6.695049296875Q19.303583437500002,6.876379296875,19.1898134375,7.044679296875Q19.0772134375,7.214159296875,18.9480334375,7.371789296875Q18.817663437500002,7.529419296875,18.6742534375,7.674009296875Q18.5296634375,7.817419296875,18.3720334375,7.946599296875Q18.2144034375,8.076969296875,18.0449234375,8.189569296875Q17.8766234375,8.303339296875,17.6964734375,8.399339296874999Q17.5163234375,8.494159296875,17.3290734375,8.573569296875Q17.1394434375,8.650599296875,16.9450734375,8.709859296875Q16.7495134375,8.769119296875,16.5504034375,8.809419296875Q16.3501034375,8.848529296875,16.1474434375,8.868679296875Q15.9447734375,8.888829296875,15.740923437500001,8.888829296875Q15.5370734375,8.888829296875,15.3344034375,8.868679296875Q15.1317334375,8.848529296875,14.9314434375,8.809419296875Q14.7323334375,8.769119296875,14.536773437499999,8.709859296875Q14.3412134375,8.650599296875,14.1539634375,8.573569296875Q13.9643334375,8.495339296874999,13.7865534375,8.399339296874999Q13.6052234375,8.303339296875,13.4357334375,8.189569296875Q13.2674434375,8.076969296875,13.1098134375,7.947789296875Q12.9521834375,7.817419296875,12.8075934375,7.674009296875Q12.6641834375,7.529419296875,12.5349984375,7.371789296875Q12.4046284375,7.214159296875,12.292035437500001,7.044679296875Q12.1782574375,6.876379296875,12.0822584375,6.696229296875Q11.9874434375,6.516079296875,11.9080364375,6.328829296875Q11.8310004375,6.139199296875,11.7717404375,5.944829296875Q11.7124814375,5.749269296875,11.6721851375,5.550159296875Q11.6330738375,5.349859296875,11.6129255375,5.147199296875Q11.59277714482,4.944529296875,11.5927734375,4.740679296875Q11.5927734375,4.536829296875,11.6129218375,4.334159296875Q11.6330701375,4.131489296874999,11.6721814375,3.931199296875Q11.7124784375,3.732089296875,11.7717364375,3.536529296875Q11.8309964375,3.340969296875,11.9080324375,3.153719296875Q11.9862554375,2.964089296875,12.0822544375,2.786309296875Q12.1782534375,2.604979296875,12.2920314375,2.4354892968749997Q12.4046244375,2.267199296875,12.5338104375,2.1095692968749997Q12.6641834375,1.951939296875,12.8075834375,1.807349296875Q12.9521834375,1.663939296875,13.1098134375,1.534754296875Q13.2674434375,1.404384296875,13.436923437499999,1.291791296875Q13.6052134375,1.1780132968750001,13.7853634375,1.082014296875Q13.9655134375,0.9871992968750001,14.1527734375,0.9077922968750001Q14.3424034375,0.830756296875,14.536773437499999,0.771496296875Q14.7323334375,0.712237296875,14.9314434375,0.671940996875Q15.1317334375,0.632829696875,15.3344034375,0.612681396875Q15.5370734375,0.592533004195,15.740923437500001,0.592529296875Q15.9447734375,0.592529296875,16.1474434375,0.612677696875Q16.3501034375,0.632825996875,16.5504034375,0.671937296875Q16.7495134375,0.712234296875,16.9450734375,0.771492296875Q17.1406234375,0.830752296875,17.3278834375,0.907788296875Q17.5175134375,0.986011296875,17.6952934375,1.082010296875Q17.8766234375,1.178009296875,18.0449234375,1.291787296875Q18.2144034375,1.4043802968749999,18.3720334375,1.5335662968750001Q18.5296634375,1.663939296875,18.6742534375,1.807339296875Q18.817663437500002,1.951939296875,18.9468434375,2.1095692968749997Q19.0772134375,2.267199296875,19.1898134375,2.436679296875Q19.303583437500002,2.604969296875,19.3995834375,2.785119296875Q19.4944034375,2.965269296875,19.5738134375,3.152529296875Q19.6508434375,3.342159296875,19.710103437500003,3.536529296875Q19.769363437499997,3.732089296875,19.8096634375,3.931199296875Q19.8487734375,4.131489296874999,19.8689234375,4.334159296875Q19.889073437500002,4.536829296875,19.889073437500002,4.740679296875Z"
              fill="#C00000"
              fillOpacity="1"
            />
          </g>
          <g>
            <path
              d="M19.09244,8.09244Q20.481479999999998,6.70459,20.481479999999998,4.74074Q20.481479999999998,2.77689,19.09244,1.38904Q17.70459,0,15.740739999999999,0Q13.77689,0,12.38904,1.38904Q11,2.77689,11,4.74074Q11,6.70459,12.38904,8.09244Q13.77689,9.48148,15.740739999999999,9.48148Q17.70459,9.48148,19.09244,8.09244ZM18.25452,2.22578Q19.296300000000002,3.26874,19.296300000000002,4.74074Q19.296300000000002,6.21393,18.25452,7.25452Q17.21392,8.2963,15.740739999999999,8.2963Q14.26755,8.2963,13.22696,7.25452Q12.185179999999999,6.21393,12.185179999999999,4.74074Q12.185179999999999,3.26756,13.22696,2.22696Q14.26755,1.18519,15.740739999999999,1.18519Q17.21392,1.18519,18.25452,2.22696L18.25452,2.22578Z"
              fill="#FFFFFF"
              fillOpacity="1"
            />
          </g>
        </g>
      </svg>
    )
  }
}

export function IconNotice({ hasLogin }: { hasLogin: boolean }) {
  const [isOpen, setIsOpen] = useState(false)
  const timeoutRef = useRef<NodeJS.Timeout>(null)
  const [notices, setNotices] = useState<NoticeType[]>([])

  const handleMouseEnter = () => {
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current)
    }
    setIsOpen(true)
  }

  const handleMouseLeave = () => {
    timeoutRef.current = setTimeout(() => {
      setIsOpen(false)
    }, 300)
  }

  const getNoticeList = async () => {
    const params = { page: 1, pageSize: 10 }
    try {
      const { code, data } = await noticeApi.getNoticeList(params)
      if (code === 200 && data) {
        const { records = [] } = data
        setNotices([...records])
      }
    } catch (error) {}
  }

  useEffect(() => {
    if (hasLogin) {
      getNoticeList()
    }
  }, [hasLogin])

  //是否存在消息未读
  const [hasUnreadNotice, setUnreadNotice] = useState(false)

  useEffect(() => {
    const hasUnRead = notices.length > 0 && notices.some((item) => !item.read)
    setUnreadNotice(hasUnRead)
  }, [notices])

  const handleClick = (val: NoticeType) => {
    //更新消息列表数据
    if (!val.read) {
      setNotices((pre) => {
        return pre.map((i) => {
          if (i.id === val.id) {
            return { ...i, read: 1 }
          }
          return i
        })
      })
    }
  }

  return (
    <IconButton onMouseEnter={handleMouseEnter} onMouseLeave={handleMouseLeave}>
      <Icon hasUnRead={hasUnreadNotice} />
      {true && (
        <div className="absolute top-[52px] w-[320px] rounded-lg text-left shadow-[0px_2px_8px_0px_rgba(0,0,0,0.1)] before:absolute before:left-[50%] before:top-[-8px] before:h-[15px] before:w-[15px] before:translate-x-[-50%] before:rotate-45 before:bg-[#fff] before:shadow-[0px_2px_8px_0px_rgba(0,0,0,0.1)] before:content-[''] after:absolute after:left-[50%] after:top-0 after:h-[20px] after:w-[40px] after:translate-x-[-50%] after:bg-[#fff] after:content-['']">
          <Notifications
            {...{ notices: notices, hasLogin: hasLogin }}
            handleNoticesClick={handleClick}
          />
        </div>
      )}
    </IconButton>
  )
}

export default IconNotice
